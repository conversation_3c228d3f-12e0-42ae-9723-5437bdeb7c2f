<?php
session_start();
if (!isset($_SESSION["empresa"])) {
    header("Location: login.php");
    exit;
}

require_once("../app/controllers/clientesController.php");

$edit = false;
$nome = $telefone = $email = $endereco = "";
$id = null;

if (isset($_GET["id"])) {
    $edit = true;
    $conn = getEmpresaConnection($_SESSION["empresa"]);
    $stmt = $conn->prepare("SELECT * FROM clientes WHERE id = :id");
    $stmt->bindParam(":id", $_GET["id"]);
    $stmt->execute();
    $cliente = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($cliente) {
        $id = $cliente["id"];
        $nome = $cliente["nome"];
        $telefone = $cliente["telefone"];
        $email = $cliente["email"];
        $endereco = $cliente["endereco"];
    }
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $nome = $_POST["nome"];
    $telefone = $_POST["telefone"];
    $email = $_POST["email"];
    $endereco = $_POST["endereco"];

    if ($edit) {
        updateCliente($_SESSION["empresa"], $id, $nome, $telefone, $email, $endereco);
    } else {
        addCliente($_SESSION["empresa"], $nome, $telefone, $email, $endereco);
    }
    header("Location: clientes.php");
    exit;
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title><?= $edit ? "Editar Cliente" : "Novo Cliente" ?> - ERP Itapira</title>
</head>
<body>
    <h1><?= $edit ? "Editar Cliente" : "Novo Cliente" ?></h1>
    <form method="POST">
        <label>Nome:</label><br>
        <input type="text" name="nome" value="<?= $nome ?>" required><br><br>
        <label>Telefone:</label><br>
        <input type="text" name="telefone" value="<?= $telefone ?>"><br><br>
        <label>Email:</label><br>
        <input type="email" name="email" value="<?= $email ?>"><br><br>
        <label>Endereço:</label><br>
        <textarea name="endereco"><?= $endereco ?></textarea><br><br>
        <button type="submit">Salvar</button>
    </form>
    <br>
    <a href="clientes.php">Voltar</a>
</body>
</html>
