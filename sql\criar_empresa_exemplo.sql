-- Criar empresa de exemplo
\c erp_master;

-- Inserir empresa no master
INSERT INTO empresas (nome, cnpj, cidade, banco_nome, ativa) 
VALUES ('Empresa Exemplo Ltda', '12.345.678/0001-90', 'Itapira', 'erp_empresa_1', TRUE);

-- <PERSON><PERSON>r banco da empresa
CREATE DATABASE erp_empresa_1
    WITH 
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'pt_BR.UTF-8'
    LC_CTYPE = 'pt_BR.UTF-8'
    TEMPLATE template0;

\c erp_empresa_1;

-- Habilitar extensão de criptografia
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Usu<PERSON><PERSON><PERSON> da empresa
CREATE TABLE usuarios (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    senha VARCHAR(255) NOT NULL,
    nivel_acesso VARCHAR(20) DEFAULT 'usuario', -- admin, gerente, usuario
    ativo BOOLEAN DEFAULT TRUE
);

-- Clientes
CREATE TABLE clientes (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(150) NOT NULL,
    telefone VARCHAR(20),
    email VARCHAR(100),
    endereco TEXT
);

-- Produtos
CREATE TABLE produtos (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(150) NOT NULL,
    preco DECIMAL(10,2) NOT NULL,
    estoque INTEGER DEFAULT 0,
    descricao TEXT
);

-- Pedidos
CREATE TABLE pedidos (
    id SERIAL PRIMARY KEY,
    cliente_id INTEGER REFERENCES clientes(id),
    data_pedido TIMESTAMP DEFAULT NOW(),
    status VARCHAR(20) DEFAULT 'pendente', -- pendente, processando, concluido, cancelado
    total DECIMAL(10,2) DEFAULT 0
);

-- Itens do pedido
CREATE TABLE pedido_itens (
    id SERIAL PRIMARY KEY,
    pedido_id INTEGER REFERENCES pedidos(id),
    produto_id INTEGER REFERENCES produtos(id),
    quantidade INTEGER NOT NULL,
    preco_unitario DECIMAL(10,2) NOT NULL
);

-- Financeiro
CREATE TABLE financeiro (
    id SERIAL PRIMARY KEY,
    tipo VARCHAR(20) NOT NULL, -- receita, despesa
    descricao VARCHAR(200) NOT NULL,
    valor DECIMAL(10,2) NOT NULL,
    data_vencimento DATE,
    data_pagamento DATE,
    status VARCHAR(20) DEFAULT 'pendente', -- pendente, pago, vencido
    pedido_id INTEGER REFERENCES pedidos(id)
);

-- Inserir usuário admin da empresa
INSERT INTO usuarios (nome, email, senha, nivel_acesso) 
VALUES ('Administrador', '<EMAIL>', crypt('123456', gen_salt('bf')), 'admin');

-- Inserir alguns dados de exemplo
INSERT INTO clientes (nome, telefone, email, endereco) VALUES
('João Silva', '(19) 99999-1111', '<EMAIL>', 'Rua A, 123 - Itapira/SP'),
('Maria Santos', '(19) 99999-2222', '<EMAIL>', 'Rua B, 456 - Itapira/SP'),
('Pedro Oliveira', '(19) 99999-3333', '<EMAIL>', 'Rua C, 789 - Itapira/SP');

INSERT INTO produtos (nome, preco, estoque, descricao) VALUES
('Produto A', 29.90, 100, 'Descrição do produto A'),
('Produto B', 49.90, 50, 'Descrição do produto B'),
('Produto C', 79.90, 25, 'Descrição do produto C');
