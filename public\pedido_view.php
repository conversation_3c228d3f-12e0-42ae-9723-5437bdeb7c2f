<?php
session_start();
if (!isset($_SESSION["empresa"])) {
    header("Location: login.php");
    exit;
}

require_once(__DIR__ . "/../app/controllers/pedidosController.php");

$pedido = getPedido($_SESSION["empresa"], $_GET["id"]);
$itens = getItensPedido($_SESSION["empresa"], $_GET["id"]);
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Pedido #<?= $pedido["id"] ?> - ERP Itapira</title>
</head>
<body>
    <h1>Pedido #<?= $pedido["id"] ?></h1>
    <p>Cliente ID: <?= $pedido["cliente_id"] ?></p>
    <p>Data: <?= $pedido["data"] ?></p>
    <p>Total: R$ <?= number_format($pedido["total"], 2, ',', '.') ?></p>
    <hr>
    <h3>Itens</h3>
    <table border="1" cellpadding="5" cellspacing="0">
        <tr>
            <th>Produto</th>
            <th>Preço</th>
            <th>Qtd</th>
            <th>Subtotal</th>
        </tr>
        <?php foreach ($itens as $i): ?>
        <tr>
            <td><?= $i["descricao"] ?></td>
            <td>R$ <?= number_format($i["preco"], 2, ',', '.') ?></td>
            <td><?= $i["quantidade"] ?></td>
            <td>R$ <?= number_format($i["preco"] * $i["quantidade"], 2, ',', '.') ?></td>
        </tr>
        <?php endforeach; ?>
    </table>
    <br>
    <a href="pedidos.php">Voltar</a>
</body>
</html>
