<?php
session_start();
if (!isset($_SESSION["empresa"])) {
    header("Location: login.php");
    exit;
}

require_once(__DIR__ . "/../app/controllers/pedidosController.php");
require_once(__DIR__ . "/../app/config/conexao.php");

$conn = getEmpresaConnection($_SESSION["empresa"]);
$clientes = $conn->query("SELECT * FROM clientes ORDER BY nome")->fetchAll(PDO::FETCH_ASSOC);
$produtos = $conn->query("SELECT * FROM produtos ORDER BY descricao")->fetchAll(PDO::FETCH_ASSOC);

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $cliente_id = $_POST["cliente_id"];
    $itens = [];
    foreach ($_POST["produto_id"] as $index => $produto_id) {
        $quantidade = $_POST["quantidade"][$index];
        $preco = $_POST["preco"][$index];
        if ($quantidade > 0) {
            $itens[] = [
                "produto_id" => $produto_id,
                "quantidade" => $quantidade,
                "preco" => $preco
            ];
        }
    }
    $pedido_id = addPedido($_SESSION["empresa"], $cliente_id, $itens);
    header("Location: pedido_view.php?id=" . $pedido_id);
    exit;
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Novo Pedido - ERP Itapira</title>
</head>
<body>
    <h1>Novo Pedido</h1>
    <form method="POST">
        <label>Cliente:</label><br>
        <select name="cliente_id" required>
            <option value="">-- selecione --</option>
            <?php foreach ($clientes as $c): ?>
                <option value="<?= $c["id"] ?>"><?= $c["nome"] ?></option>
            <?php endforeach; ?>
        </select><br><br>

        <h3>Itens</h3>
        <table border="1" cellpadding="5" cellspacing="0">
            <tr>
                <th>Produto</th>
                <th>Preço</th>
                <th>Qtd</th>
            </tr>
            <?php foreach ($produtos as $p): ?>
            <tr>
                <td>
                    <?= $p["descricao"] ?>
                    <input type="hidden" name="produto_id[]" value="<?= $p["id"] ?>">
                </td>
                <td>
                    R$ <?= number_format($p["preco"], 2, ',', '.') ?>
                    <input type="hidden" name="preco[]" value="<?= $p["preco"] ?>">
                </td>
                <td><input type="number" name="quantidade[]" value="0" min="0"></td>
            </tr>
            <?php endforeach; ?>
        </table><br>
        <button type="submit">Salvar Pedido</button>
    </form>
    <br>
    <a href="pedidos.php">Voltar</a>
</body>
</html>
