<?php
session_start();
if (!isset($_SESSION["empresa"])) {
    header("Location: login.php");
    exit;
}

require_once(__DIR__ . "/../app/controllers/financeiroController.php");

$edit = false;
$id = null;
$tipo = $descricao = $valor = $vencimento = $status = "";

if (isset($_GET["id"])) {
    $edit = true;
    $conn = getEmpresaConnection($_SESSION["empresa"]);
    $stmt = $conn->prepare("SELECT * FROM financeiro WHERE id = :id");
    $stmt->execute([":id" => $_GET["id"]]);
    $l = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($l) {
        $id = $l["id"];
        $tipo = $l["tipo"];
        $descricao = $l["descricao"];
        $valor = $l["valor"];
        $vencimento = $l["vencimento"];
        $status = $l["status"];
    }
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $tipo = $_POST["tipo"];
    $descricao = $_POST["descricao"];
    $valor = $_POST["valor"];
    $vencimento = $_POST["vencimento"];
    $status = $_POST["status"];

    if ($edit) {
        updateLancamento($_SESSION["empresa"], $id, $tipo, $descricao, $valor, $vencimento, $status);
    } else {
        addLancamento($_SESSION["empresa"], $tipo, $descricao, $valor, $vencimento, $status);
    }
    header("Location: financeiro.php");
    exit;
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title><?= $edit ? "Editar Lançamento" : "Novo Lançamento" ?></title>
</head>
<body>
    <h1><?= $edit ? "Editar Lançamento" : "Novo Lançamento" ?></h1>
    <form method="POST">
        <label>Tipo:</label><br>
        <select name="tipo">
            <option value="pagar" <?= $tipo == "pagar" ? "selected" : "" ?>>Pagar</option>
            <option value="receber" <?= $tipo == "receber" ? "selected" : "" ?>>Receber</option>
        </select><br><br>

        <label>Descrição:</label><br>
        <input type="text" name="descricao" value="<?= $descricao ?>" required><br><br>

        <label>Valor:</label><br>
        <input type="number" step="0.01" name="valor" value="<?= $valor ?>" required><br><br>

        <label>Vencimento:</label><br>
        <input type="date" name="vencimento" value="<?= $vencimento ?>"><br><br>

        <label>Status:</label><br>
        <select name="status">
            <option value="pendente" <?= $status == "pendente" ? "selected" : "" ?>>Pendente</option>
            <option value="pago" <?= $status == "pago" ? "selected" : "" ?>>Pago</option>
            <option value="cancelado" <?= $status == "cancelado" ? "selected" : "" ?>>Cancelado</option>
        </select><br><br>

        <button type="submit">Salvar</button>
    </form>
    <br>
    <a href="financeiro.php">Voltar</a>
</body>
</html>
