<?php
session_start();
if (!isset($_SESSION["empresa"])) {
    header("Location: login.php");
    exit;
}

require_once(__DIR__ . "/../app/controllers/produtosController.php");

$edit = false;
$descricao = $preco = $estoque = "";
$id = null;

if (isset($_GET["id"])) {
    $edit = true;
    $conn = getEmpresaConnection($_SESSION["empresa"]);
    $stmt = $conn->prepare("SELECT * FROM produtos WHERE id = :id");
    $stmt->bindParam(":id", $_GET["id"]);
    $stmt->execute();
    $produto = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($produto) {
        $id = $produto["id"];
        $descricao = $produto["descricao"];
        $preco = $produto["preco"];
        $estoque = $produto["estoque"];
    }
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $descricao = $_POST["descricao"];
    $preco = $_POST["preco"];
    $estoque = $_POST["estoque"];

    if ($edit) {
        updateProduto($_SESSION["empresa"], $id, $descricao, $preco, $estoque);
    } else {
        addProduto($_SESSION["empresa"], $descricao, $preco, $estoque);
    }
    header("Location: produtos.php");
    exit;
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title><?= $edit ? "Editar Produto" : "Novo Produto" ?> - ERP Itapira</title>
</head>
<body>
    <h1><?= $edit ? "Editar Produto" : "Novo Produto" ?></h1>
    <form method="POST">
        <label>Descrição:</label><br>
        <input type="text" name="descricao" value="<?= $descricao ?>" required><br><br>
        <label>Preço:</label><br>
        <input type="number" step="0.01" name="preco" value="<?= $preco ?>"><br><br>
        <label>Estoque:</label><br>
        <input type="number" name="estoque" value="<?= $estoque ?>"><br><br>
        <button type="submit">Salvar</button>
    </form>
    <br>
    <a href="produtos.php">Voltar</a>
</body>
</html>
