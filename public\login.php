<?php
require_once("../app/config/master.php");
require_once("../app/config/conexao.php");
session_start();

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $email = $_POST["email"];
    $senha = $_POST["senha"];

    $connMaster = getMasterConnection();
    $stmt = $connMaster->prepare("SELECT * FROM empresas WHERE ativa = TRUE");
    $stmt->execute();
    $empresas = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($empresas as $empresa) {
        $connEmpresa = getEmpresaConnection($empresa["banco_nome"]);
        $stmtUser = $connEmpresa->prepare("SELECT * FROM usuarios WHERE email = :email AND ativo = TRUE");
        $stmtUser->bindParam(":email", $email);
        $stmtUser->execute();
        $usuario = $stmtUser->fetch(PDO::FETCH_ASSOC);

        if ($usuario && password_verify($senha, $usuario["senha"])) {
            $_SESSION["empresa"] = $empresa["banco_nome"];
            $_SESSION["usuario"] = $usuario["nome"];
            header("Location: dashboard.php");
            exit;
        }
    }

    $erro = "Usuário ou senha inválidos!";
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Login ERP Itapira</title>
</head>
<body>
    <h2>Login ERP Itapira</h2>
    <?php if (!empty($erro)) echo "<p style='color:red;'>$erro</p>"; ?>
    <form method="POST">
        <label>Email:</label><br>
        <input type="text" name="email" required><br><br>
        <label>Senha:</label><br>
        <input type="password" name="senha" required><br><br>
        <button type="submit">Entrar</button>
    </form>
</body>
</html>
