<?php
session_start();
if (!isset($_SESSION["empresa"])) {
    header("Location: login.php");
    exit;
}
require_once(__DIR__ . "/../app/controllers/clientesController.php");
$clientes = getClientes($_SESSION["empresa"]);
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Clientes - Nextor ERP</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
<div class="wrapper">
    <?php include "layout/sidebar.php"; ?>
    <div class="main">
        <?php include "layout/header.php"; ?>
        <div class="container">
            <div class="card">
                <h2><i class="fas fa-users"></i> Clientes</h2>

                <div class="produtos-grid">
                    <?php foreach ($clientes as $c): ?>
                        <div class="produto-card">
                            <h3><i class="fas fa-user"></i> <?= $c->nome ?></h3>
                            <p><i class="fas fa-envelope"></i> <?= $c->email ?></p>
                            <p><i class="fas fa-phone"></i> <?= $c->telefone ?></p>
                            <span class="badge badge-success">Ativo</span>

                            <div class="card-actions">
                                <a href="cliente_form.php?id=<?= $c->id ?>" class="btn"><i class="fas fa-edit"></i> Editar</a>
                                <a href="cliente_delete.php?id=<?= $c->id ?>" class="btn btn-danger" onclick="return confirm('Excluir cliente?')"><i class="fas fa-trash"></i> Excluir</a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php include "layout/footer.php"; ?>
    </div>
</div>

<a href="cliente_form.php" class="fab"><i class="fas fa-plus"></i></a>

</body>
</html>
