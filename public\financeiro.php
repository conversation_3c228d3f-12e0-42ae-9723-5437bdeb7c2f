<?php
session_start();
if (!isset($_SESSION["empresa"])) {
    header("Location: login.php");
    exit;
}
require_once(__DIR__ . "/../app/controllers/financeiroController.php");
$lancamentos = getLancamentos($_SESSION["empresa"]);
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Financeiro - Nextor ERP</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div class="wrapper">
    <?php include "layout/sidebar.php"; ?>
    <div class="main">
        <?php include "layout/header.php"; ?>
        <div class="container">
            <div class="card">
                <h2><i class="fas fa-wallet"></i> Financeiro</h2>
                <a href="lancamento_form.php" class="btn"><i class="fas fa-plus"></i> Novo Lançamento</a>
                <table>
                    <tr>
                        <th>ID</th><th>Tipo</th><th>Descrição</th><th>Valor</th><th>Vencimento</th><th>Status</th><th>Ações</th>
                    </tr>
                    <?php foreach ($lancamentos as $l): ?>
                    <tr>
                        <td><?= $l->id ?></td>
                        <td><?= ucfirst($l->tipo) ?></td>
                        <td><?= $l->descricao ?></td>
                        <td>R$ <?= number_format($l->valor,2,",",".") ?></td>
                        <td><?= $l->vencimento ?></td>
                        <td><?= $l->status ?></td>
                        <td>
                            <a href="lancamento_form.php?id=<?= $l->id ?>" class="btn"><i class="fas fa-edit"></i> Editar</a>
                            <a href="lancamento_delete.php?id=<?= $l->id ?>" class="btn btn-danger" onclick="return confirm('Excluir lançamento?')"><i class="fas fa-trash"></i> Excluir</a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </table>
            </div>
        </div>
        <?php include "layout/footer.php"; ?>
    </div>
</div>
</body>
</html>
