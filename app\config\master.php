<?php
// Conexão com banco master
function getMasterConnection() {
    $host = "localhost";
    $port = "5432";
    $dbname = "erp_master";
    $user = "postgres";   // seu usuário do postgres
    $password = "postgres"; // senha do postgres

    try {
        $conn = new PDO("pgsql:host=$host;port=$port;dbname=$dbname", $user, $password);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $conn;
    } catch (PDOException $e) {
        die("Erro de conexão com banco master: " . $e->getMessage());
    }
}
?>
