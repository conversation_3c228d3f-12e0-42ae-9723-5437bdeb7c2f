/* Variáveis CSS */
:root {
    --primary: #2c3e50;
    --secondary: #27ae60;
    --danger: #e74c3c;
    --warning: #f39c12;
    --info: #3498db;
    --light: #ecf0f1;
    --dark: #34495e;
    --white: #ffffff;
    --shadow: 0 4px 6px rgba(0,0,0,0.1);
    --gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Reset e base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--light);
    color: var(--dark);
    line-height: 1.6;
    display: flex;
    min-height: 100vh;
}

/* Layout principal */
.wrapper {
    display: flex;
    width: 100%;
}

/* Sidebar */
.sidebar {
    width: 250px;
    background: var(--primary);
    color: var(--white);
    padding: 20px 0;
    box-shadow: var(--shadow);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
}

.sidebar h2 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 24px;
    font-weight: 300;
    padding: 0 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    padding-bottom: 20px;
}

.sidebar a {
    padding: 12px 20px;
    color: var(--white);
    text-decoration: none;
    display: flex;
    align-items: center;
    transition: background 0.3s, transform 0.2s;
    border-left: 3px solid transparent;
}

.sidebar a i {
    margin-right: 10px;
    font-size: 16px;
}

.sidebar a:hover {
    background: rgba(255,255,255,0.1);
    border-left: 3px solid var(--secondary);
    transform: translateX(4px);
}

/* Header */
header {
    background: var(--white);
    padding: 15px 20px;
    box-shadow: var(--shadow);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

header h1 {
    font-size: 20px;
    margin: 0;
    color: var(--primary);
}

/* Main */
.main {
    flex: 1;
    margin-left: 250px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.container {
    flex: 1;
    padding: 20px;
}

/* Cards */
.card {
    background: var(--white);
    padding: 20px;
    border-radius: 12px;
    box-shadow: var(--shadow);
    margin-bottom: 20px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 18px rgba(0,0,0,0.15);
}

/* Botões */
.btn {
    background: var(--secondary);
    color: var(--white);
    border: none;
    padding: 10px 16px;
    border-radius: 6px;
    text-decoration: none;
    cursor: pointer;
    transition: background 0.3s, transform 0.2s;
    display: inline-flex;
    align-items: center;
    font-size: 14px;
}

.btn i {
    margin-right: 6px;
}

.btn:hover {
    background: #219150;
    transform: translateY(-2px);
}

.btn-danger {
    background: var(--danger);
}

.btn-danger:hover {
    background: #c0392b;
}

/* Botão flutuante (FAB) */
.fab {
    position: fixed;
    bottom: 25px;
    right: 25px;
    background: var(--secondary);
    color: var(--white);
    font-size: 22px;
    width: 55px;
    height: 55px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    text-decoration: none;
    box-shadow: var(--shadow);
    transition: 0.3s;
    z-index: 1000;
}

.fab:hover {
    background: #219150;
    transform: scale(1.1);
}

/* Grids de cards */
.produtos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.produto-card {
    background: var(--white);
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--shadow);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.produto-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 18px rgba(0,0,0,0.15);
}

.produto-card h3 {
    margin: 0;
    font-size: 18px;
    color: var(--primary);
    display: flex;
    align-items: center;
}

.produto-card h3 i {
    margin-right: 8px;
    color: var(--secondary);
}

.produto-preco {
    font-size: 20px;
    font-weight: bold;
    color: var(--secondary);
    margin: 10px 0;
}

/* Estoque/Badges */
.estoque {
    font-size: 14px;
    font-weight: bold;
    padding: 5px 10px;
    border-radius: 20px;
    display: inline-block;
    margin-top: 10px;
}

.estoque-alto {
    background: #d0f0fd;
    color: #0275d8;
}

.estoque-medio {
    background: #fff3cd;
    color: #856404;
}

.estoque-baixo {
    background: #f8d7da;
    color: #721c24;
}

.badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    display: inline-block;
}

.badge-success { background: #d4edda; color: #155724; }
.badge-warning { background: #fff3cd; color: #856404; }
.badge-danger { background: #f8d7da; color: #721c24; }

/* Ações nos cards */
.card-actions {
    margin-top: 15px;
    display: flex;
    justify-content: space-between;
}

.card-actions a {
    flex: 1;
    margin: 0 5px;
    text-align: center;
}

/* Tabelas */
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

table th {
    background: var(--primary);
    color: var(--white);
    padding: 12px;
    text-align: left;
}

table td {
    padding: 12px;
    border-bottom: 1px solid #eee;
    background: var(--white);
    transition: background 0.2s;
}

table tr:hover td {
    background: #f9f9f9;
}

/* Formulários */
input, select, textarea {
    width: 100%;
    padding: 12px;
    margin-top: 5px;
    margin-bottom: 15px;
    border-radius: 6px;
    border: 1px solid #ccc;
    font-size: 14px;
    transition: border 0.2s;
}

input:focus, select:focus, textarea:focus {
    border: 1px solid var(--secondary);
    outline: none;
}

/* Footer */
footer {
    text-align: center;
    padding: 12px;
    background: var(--white);
    font-size: 12px;
    color: #777;
    box-shadow: var(--shadow);
}

/* Responsivo */
@media (max-width: 768px) {
    .sidebar {
        width: 70px;
    }
    .sidebar h2, .sidebar img, .sidebar a span {
        display: none;
    }
    .sidebar a {
        justify-content: center;
    }
    .main {
        margin-left: 70px;
    }
}

@media (max-width: 480px) {
    .sidebar {
        width: 60px;
    }
    .main {
        margin-left: 60px;
    }
    .container {
        padding: 15px;
    }
}
