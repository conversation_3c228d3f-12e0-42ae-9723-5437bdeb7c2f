<?php
require_once(__DIR__ . "/../config/conexao.php");
require_once(__DIR__ . "/../models/Lancamento.php");

function getLancamentos($empresaBanco) {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->query("SELECT * FROM financeiro ORDER BY vencimento ASC");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function addLancamento($empresaBanco, $tipo, $descricao, $valor, $vencimento, $status = "pendente") {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->prepare("INSERT INTO financeiro (tipo, descricao, valor, vencimento, status) 
                            VALUES (:tipo, :descricao, :valor, :vencimento, :status)");
    $stmt->execute([
        ":tipo" => $tipo,
        ":descricao" => $descricao,
        ":valor" => $valor,
        ":vencimento" => $vencimento,
        ":status" => $status
    ]);
}

function updateLancamento($empresaBanco, $id, $tipo, $descricao, $valor, $vencimento, $status) {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->prepare("UPDATE financeiro 
                            SET tipo=:tipo, descricao=:descricao, valor=:valor, vencimento=:vencimento, status=:status 
                            WHERE id=:id");
    $stmt->execute([
        ":tipo" => $tipo,
        ":descricao" => $descricao,
        ":valor" => $valor,
        ":vencimento" => $vencimento,
        ":status" => $status,
        ":id" => $id
    ]);
}

function deleteLancamento($empresaBanco, $id) {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->prepare("DELETE FROM financeiro WHERE id = :id");
    $stmt->execute([":id" => $id]);
}
?>
