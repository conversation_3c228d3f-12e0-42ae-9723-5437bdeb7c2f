<?php
require_once(__DIR__ . "/../config/conexao.php");
require_once(__DIR__ . "/../models/Pedido.php");

function getPedidos($empresaBanco) {
    $conn = getEmpresaConnection($empresaBanco);
    $sql = "SELECT p.*, c.nome as cliente_nome 
            FROM pedidos p
            LEFT JOIN clientes c ON c.id = p.cliente_id
            ORDER BY p.id DESC";
    $stmt = $conn->query($sql);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getPedido($empresaBanco, $id) {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->prepare("SELECT * FROM pedidos WHERE id = :id");
    $stmt->bindParam(":id", $id);
    $stmt->execute();
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function getItensPedido($empresaBanco, $pedido_id) {
    $conn = getEmpresaConnection($empresaBanco);
    $sql = "SELECT pi.*, pr.descricao 
            FROM pedidos_itens pi
            JOIN produtos pr ON pr.id = pi.produto_id
            WHERE pi.pedido_id = :pedido_id";
    $stmt = $conn->prepare($sql);
    $stmt->bindParam(":pedido_id", $pedido_id);
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function addPedido($empresaBanco, $cliente_id, $itens) {
    $conn = getEmpresaConnection($empresaBanco);
    $conn->beginTransaction();
    try {
        $stmt = $conn->prepare("INSERT INTO pedidos (cliente_id, total) VALUES (:cliente_id, 0) RETURNING id");
        $stmt->bindParam(":cliente_id", $cliente_id);
        $stmt->execute();
        $pedido_id = $stmt->fetchColumn();

        $total = 0;
        foreach ($itens as $item) {
            $subtotal = $item["preco"] * $item["quantidade"];
            $total += $subtotal;

            $stmtItem = $conn->prepare("INSERT INTO pedidos_itens (pedido_id, produto_id, quantidade, preco) 
                                        VALUES (:pedido_id, :produto_id, :quantidade, :preco)");
            $stmtItem->execute([
                ":pedido_id" => $pedido_id,
                ":produto_id" => $item["produto_id"],
                ":quantidade" => $item["quantidade"],
                ":preco" => $item["preco"]
            ]);

            // Atualizar estoque
            $stmtEstoque = $conn->prepare("UPDATE produtos SET estoque = estoque - :qtd WHERE id = :produto_id");
            $stmtEstoque->execute([
                ":qtd" => $item["quantidade"],
                ":produto_id" => $item["produto_id"]
            ]);
        }

        $stmtUpdate = $conn->prepare("UPDATE pedidos SET total = :total WHERE id = :id");
        $stmtUpdate->execute([":total" => $total, ":id" => $pedido_id]);

        $conn->commit();
        return $pedido_id;
    } catch (Exception $e) {
        $conn->rollBack();
        throw $e;
    }
}

// Após atualizar o total do pedido:
$stmtUpdate = $conn->prepare("UPDATE pedidos SET total = :total WHERE id = :id");
$stmtUpdate->execute([":total" => $total, ":id" => $pedido_id]);

// 🔹 Inserir no financeiro (conta a receber)
$stmtFinanceiro = $conn->prepare("INSERT INTO financeiro (tipo, descricao, valor, vencimento, status) 
                                  VALUES ('receber', :descricao, :valor, CURRENT_DATE + INTERVAL '7 days', 'pendente')");
$stmtFinanceiro->execute([
    ":descricao" => "Pedido #$pedido_id",
    ":valor" => $total
]);


function deletePedido($empresaBanco, $id) {
    $conn = getEmpresaConnection($empresaBanco);
    $stmt = $conn->prepare("DELETE FROM pedidos_itens WHERE pedido_id = :id");
    $stmt->execute([":id" => $id]);

    $stmt = $conn->prepare("DELETE FROM pedidos WHERE id = :id");
    $stmt->execute([":id" => $id]);
}
?>
