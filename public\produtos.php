<?php
session_start();
if (!isset($_SESSION["empresa"])) {
    header("Location: login.php");
    exit;
}
require_once(__DIR__ . "/../app/controllers/produtosController.php");
$produtos = getProdutos($_SESSION["empresa"]);
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Produtos - Nextor ERP</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
<div class="wrapper">
    <?php include "layout/sidebar.php"; ?>
    <div class="main">
        <?php include "layout/header.php"; ?>
        <div class="container">
            <div class="card">
                <h2><i class="fas fa-box"></i> Produtos</h2>

                <div class="produtos-grid">
                    <?php foreach ($produtos as $p): ?>
                        <div class="produto-card">
                            <h3><i class="fas fa-cube"></i> <?= $p->descricao ?></h3>
                            <p class="produto-preco">R$ <?= number_format($p->preco,2,",",".") ?></p>
                            
                            <?php
                                $estoqueClass = "estoque-alto";
                                $estoqueLabel = "Estoque Alto";
                                if ($p->estoque <= 5) {
                                    $estoqueClass = "estoque-baixo";
                                    $estoqueLabel = "Estoque Baixo";
                                } elseif ($p->estoque <= 20) {
                                    $estoqueClass = "estoque-medio";
                                    $estoqueLabel = "Estoque Médio";
                                }
                            ?>
                            <span class="estoque <?= $estoqueClass ?>">📦 <?= $estoqueLabel ?> (<?= $p->estoque ?>)</span>

                            <div class="card-actions">
                                <a href="produto_form.php?id=<?= $p->id ?>" class="btn"><i class="fas fa-edit"></i> Editar</a>
                                <a href="produto_delete.php?id=<?= $p->id ?>" class="btn btn-danger" onclick="return confirm('Excluir produto?')"><i class="fas fa-trash"></i> Excluir</a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php include "layout/footer.php"; ?>
    </div>
</div>

<a href="produto_form.php" class="fab"><i class="fas fa-plus"></i></a>

</body>
</html>
