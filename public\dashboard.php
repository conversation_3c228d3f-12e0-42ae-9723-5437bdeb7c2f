<?php
session_start();
if (!isset($_SESSION["empresa"])) {
    header("Location: login.php");
    exit;
}

// Aqui você pode puxar estatísticas do banco:
$stats = [
    "clientes" => 120,
    "produtos" => 350,
    "pedidos" => 45,
    "receita" => 15230.75
];
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Dashboard - Nextor ERP</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
<div class="wrapper">
    <?php include "layout/sidebar.php"; ?>
    <div class="main">
        <?php include "layout/header.php"; ?>
        <div class="container">

            <div class="produtos-grid">
                <div class="produto-card">
                    <h3><i class="fas fa-users"></i> Clientes</h3>
                    <p class="produto-preco"><?= $stats["clientes"] ?></p>
                </div>
                <div class="produto-card">
                    <h3><i class="fas fa-box"></i> Produtos</h3>
                    <p class="produto-preco"><?= $stats["produtos"] ?></p>
                </div>
                <div class="produto-card">
                    <h3><i class="fas fa-shopping-cart"></i> Pedidos</h3>
                    <p class="produto-preco"><?= $stats["pedidos"] ?></p>
                </div>
                <div class="produto-card">
                    <h3><i class="fas fa-wallet"></i> Receita</h3>
                    <p class="produto-preco">R$ <?= number_format($stats["receita"],2,",",".") ?></p>
                </div>
            </div>

        </div>
        <?php include "layout/footer.php"; ?>
    </div>
</div>
</body>
</html>
